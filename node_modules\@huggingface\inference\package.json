{"name": "@huggingface/inference", "version": "2.8.1", "packageManager": "pnpm@8.10.5", "license": "MIT", "author": "<PERSON> <tim.mikelad<PERSON>@gmail.com>", "description": "Typescript wrapper for the Hugging Face Inference Endpoints & Inference API", "repository": {"type": "git", "url": "https://github.com/huggingface/huggingface.js.git"}, "publishConfig": {"access": "public"}, "keywords": ["hugging face", "hugging face typescript", "huggingface", "huggingface-inference-api", "huggingface-inference-api-typescript", "inference", "ai"], "engines": {"node": ">=18"}, "files": ["dist", "src"], "source": "src/index.ts", "types": "./dist/src/index.d.ts", "main": "./dist/index.cjs", "module": "./dist/index.js", "exports": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "type": "module", "dependencies": {"@huggingface/tasks": "^0.12.9"}, "devDependencies": {"@types/node": "18.13.0"}, "resolutions": {}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --clean && tsc --emitDeclarationOnly --declaration", "dts": "tsx scripts/generate-dts.ts && tsc --noEmit dist/index.d.ts", "lint": "eslint --quiet --fix --ext .cjs,.ts .", "lint:check": "eslint --ext .cjs,.ts .", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest run --config vitest.config.mts", "test:browser": "vitest run --browser.name=chrome --browser.headless --config vitest.config.mts", "check": "tsc"}}