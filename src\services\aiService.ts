import { FormData, FeedbackResult } from '../types';
import { analyzeWithAI as analyzeWithGemini } from './geminiService';
import { analyzeWithNativeAI, analyzeWithHuggingFace } from './nativeAIService';

// Configuração do modo de IA
const AI_MODE = import.meta.env.VITE_AI_MODE || 'native';
const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;
const HF_TOKEN = import.meta.env.VITE_HUGGING_FACE_TOKEN;

export type AIProvider = 'native' | 'gemini' | 'huggingface' | 'auto';

// Detecta automaticamente qual IA usar baseado nas configurações
function detectAvailableAI(): AIProvider {
  // Se modo específico foi definido e está disponível
  if (AI_MODE === 'gemini' && GOOGLE_API_KEY && GOOGLE_API_KEY !== 'demo_key_replace_with_real_key') {
    return 'gemini';
  }
  
  if (AI_MODE === 'huggingface' && HF_TOKEN && HF_TOKEN !== 'demo_token_replace_with_real_token') {
    return 'huggingface';
  }
  
  if (AI_MODE === 'native') {
    return 'native';
  }
  
  // Modo automático - prioriza APIs externas se disponíveis
  if (GOOGLE_API_KEY && GOOGLE_API_KEY !== 'demo_key_replace_with_real_key') {
    return 'gemini';
  }
  
  if (HF_TOKEN && HF_TOKEN !== 'demo_token_replace_with_real_token') {
    return 'huggingface';
  }
  
  // Fallback para IA nativa (sempre disponível)
  return 'native';
}

export async function analyzeWithAI(formData: FormData): Promise<FeedbackResult> {
  const provider = detectAvailableAI();
  
  console.log(`🤖 Usando IA: ${provider}`);
  
  try {
    let result;
    
    switch (provider) {
      case 'gemini':
        result = await analyzeWithGemini(formData);
        return {
          ...result,
          isAIGenerated: true
        };
        
      case 'huggingface':
        result = await analyzeWithHuggingFace(formData);
        return {
          ...result,
          isAIGenerated: true
        };
        
      case 'native':
      default:
        result = await analyzeWithNativeAI(formData);
        return {
          ...result,
          isAIGenerated: true // IA nativa também é considerada IA real
        };
    }
  } catch (error) {
    console.error(`Erro na IA ${provider}:`, error);
    
    // Fallback para IA nativa em caso de erro
    if (provider !== 'native') {
      console.log('🔄 Fallback para IA nativa');
      const result = await analyzeWithNativeAI(formData);
      return {
        ...result,
        isAIGenerated: true
      };
    }
    
    throw error;
  }
}

// Função para obter informações sobre o provedor de IA atual
export function getAIProviderInfo(): {
  provider: AIProvider;
  name: string;
  description: string;
  requiresConfig: boolean;
} {
  const provider = detectAvailableAI();
  
  const providerInfo: Record<AIProvider, { name: string; description: string; requiresConfig: boolean }> = {
    native: {
      name: 'IA Nativa Inteligente',
      description: 'Análise avançada baseada em padrões e algoritmos proprietários. Funciona offline e não requer configuração.',
      requiresConfig: false
    },
    gemini: {
      name: 'Google Gemini AI',
      description: 'Inteligência Artificial avançada do Google. Requer API key gratuita.',
      requiresConfig: true
    },
    huggingface: {
      name: 'Hugging Face AI',
      description: 'Modelos de linguagem open-source da comunidade Hugging Face. Requer token gratuito.',
      requiresConfig: true
    },
    auto: {
      name: 'IA Automática',
      description: 'Seleciona automaticamente a melhor IA disponível.',
      requiresConfig: false
    }
  };

  return {
    provider,
    ...providerInfo[provider],
    requiresConfig: provider !== 'native'
  };
}

// Função para validar se todas as configurações necessárias estão presentes
export function validateAIConfiguration(): {
  isValid: boolean;
  provider: AIProvider;
  message: string;
} {
  const provider = detectAvailableAI();
  
  switch (provider) {
    case 'native':
      return {
        isValid: true,
        provider,
        message: 'IA Nativa configurada e pronta para uso'
      };
      
    case 'gemini':
      const hasGeminiKey = GOOGLE_API_KEY && GOOGLE_API_KEY !== 'demo_key_replace_with_real_key';
      return {
        isValid: hasGeminiKey,
        provider,
        message: hasGeminiKey 
          ? 'Google Gemini AI configurado e pronto'
          : 'Configure VITE_GOOGLE_API_KEY para usar Google Gemini'
      };
      
    case 'huggingface':
      const hasHFToken = HF_TOKEN && HF_TOKEN !== 'demo_token_replace_with_real_token';
      return {
        isValid: hasHFToken,
        provider,
        message: hasHFToken
          ? 'Hugging Face AI configurado e pronto'
          : 'Configure VITE_HUGGING_FACE_TOKEN para usar Hugging Face'
      };
      
    default:
      return {
        isValid: true,
        provider: 'native',
        message: 'Usando IA Nativa como fallback'
      };
  }
}
