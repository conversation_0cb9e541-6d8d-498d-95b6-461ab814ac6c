import type { TaskDataCustom } from "..";

const taskData: TaskDataCustom = {
	datasets: [],
	demo: {
		inputs: [],
		outputs: [],
	},
	isPlaceholder: true,
	metrics: [],
	models: [],
	spaces: [],
	summary: "",
	widgetModels: [],
	youtubeId: undefined,
	/// If this is a subtask, link to the most general task ID
	/// (eg, text2text-generation is the canonical ID of translation)
	canonicalId: undefined,
};

export default taskData;
