{"$id": "/inference/schemas/chat-completion/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Chat Completion Output.\n\nAuto-generated from TGI specs.\nFor more details, check out https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/scripts/inference-tgi-import.ts.", "title": "ChatCompletionOutput", "type": "object", "required": ["id", "created", "model", "system_fingerprint", "choices", "usage"], "properties": {"choices": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionOutputComplete"}}, "created": {"type": "integer", "format": "int64", "example": "1706270835", "minimum": 0}, "id": {"type": "string"}, "model": {"type": "string", "example": "mistralai/Mistral-7B-Instruct-v0.2"}, "system_fingerprint": {"type": "string"}, "usage": {"$ref": "#/$defs/ChatCompletionOutputUsage"}}, "$defs": {"ChatCompletionOutputComplete": {"type": "object", "required": ["index", "message", "finish_reason"], "properties": {"finish_reason": {"type": "string"}, "index": {"type": "integer", "format": "int32", "minimum": 0}, "logprobs": {"allOf": [{"$ref": "#/$defs/ChatCompletionOutputLogprobs"}], "nullable": true}, "message": {"$ref": "#/$defs/ChatCompletionOutputMessage"}}, "title": "ChatCompletionOutputComplete"}, "ChatCompletionOutputLogprobs": {"type": "object", "required": ["content"], "properties": {"content": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionOutputLogprob"}}}, "title": "ChatCompletionOutputLogprobs"}, "ChatCompletionOutputLogprob": {"type": "object", "required": ["token", "logprob", "top_logprobs"], "properties": {"logprob": {"type": "number", "format": "float"}, "token": {"type": "string"}, "top_logprobs": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionOutputTopLogprob"}}}, "title": "ChatCompletionOutputLogprob"}, "ChatCompletionOutputTopLogprob": {"type": "object", "required": ["token", "logprob"], "properties": {"logprob": {"type": "number", "format": "float"}, "token": {"type": "string"}}, "title": "ChatCompletionOutputTopLogprob"}, "ChatCompletionOutputMessage": {"oneOf": [{"$ref": "#/$defs/ChatCompletionOutputTextMessage"}, {"$ref": "#/$defs/ChatCompletionOutputToolCallMessage"}], "title": "ChatCompletionOutputMessage"}, "ChatCompletionOutputTextMessage": {"type": "object", "required": ["role", "content"], "properties": {"content": {"type": "string", "example": "My name is <PERSON> and I"}, "role": {"type": "string", "example": "user"}}, "title": "ChatCompletionOutputTextMessage"}, "ChatCompletionOutputToolCallMessage": {"type": "object", "required": ["role", "tool_calls"], "properties": {"role": {"type": "string", "example": "assistant"}, "tool_calls": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionOutputToolCall"}}}, "title": "ChatCompletionOutputToolCallMessage"}, "ChatCompletionOutputToolCall": {"type": "object", "required": ["id", "type", "function"], "properties": {"function": {"$ref": "#/$defs/ChatCompletionOutputFunctionDefinition"}, "id": {"type": "string"}, "type": {"type": "string"}}, "title": "ChatCompletionOutputToolCall"}, "ChatCompletionOutputFunctionDefinition": {"type": "object", "required": ["name", "arguments"], "properties": {"arguments": {}, "description": {"type": "string", "nullable": true}, "name": {"type": "string"}}, "title": "ChatCompletionOutputFunctionDefinition"}, "ChatCompletionOutputUsage": {"type": "object", "required": ["prompt_tokens", "completion_tokens", "total_tokens"], "properties": {"completion_tokens": {"type": "integer", "format": "int32", "minimum": 0}, "prompt_tokens": {"type": "integer", "format": "int32", "minimum": 0}, "total_tokens": {"type": "integer", "format": "int32", "minimum": 0}}, "title": "ChatCompletionOutputUsage"}}}