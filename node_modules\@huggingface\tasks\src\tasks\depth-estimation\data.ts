import type { TaskDataCustom } from "..";

const taskData: TaskDataCustom = {
	datasets: [
		{
			description: "NYU Depth V2 Dataset: Video dataset containing both RGB and depth sensor data.",
			id: "sayakpaul/nyu_depth_v2",
		},
		{
			description: "Monocular depth estimation benchmark based without noise and errors.",
			id: "depth-anything/DA-2K",
		},
	],
	demo: {
		inputs: [
			{
				filename: "depth-estimation-input.jpg",
				type: "img",
			},
		],
		outputs: [
			{
				filename: "depth-estimation-output.png",
				type: "img",
			},
		],
	},
	metrics: [],
	models: [
		{
			description: "Cutting-edge depth estimation model.",
			id: "depth-anything/Depth-Anything-V2-Large",
		},
		{
			description: "A strong monocular depth estimation model.",
			id: "jingheya/lotus-depth-g-v1-0",
		},
		{
			description: "A depth estimation model that predicts depth in videos.",
			id: "tencent/DepthCrafter",
		},
		{
			description: "A robust depth estimation model.",
			id: "apple/DepthPro",
		},
	],
	spaces: [
		{
			description: "An application that predicts the depth of an image and then reconstruct the 3D model as voxels.",
			id: "radames/dpt-depth-estimation-3d-voxels",
		},
		{
			description: "An application for bleeding-edge depth estimation.",
			id: "akhaliq/depth-pro",
		},
		{
			description: "An application on cutting-edge depth estimation in videos.",
			id: "tencent/DepthCrafter",
		},
		{
			description: "A human-centric depth estimation application.",
			id: "facebook/sapiens-depth",
		},
	],
	summary: "Depth estimation is the task of predicting depth of the objects present in an image.",
	widgetModels: [""],
	youtubeId: "",
};

export default taskData;
