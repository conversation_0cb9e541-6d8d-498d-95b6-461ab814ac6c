# 🔑 Como Configurar a API do Google Gemini

## Passo a Passo Detalhado

### 1. Acessar o Google AI Studio

1. Abra seu navegador e vá para: https://makersuite.google.com/app/apikey
2. Faça login com sua conta Google (Gmail)
3. Se for a primeira vez, aceite os termos de uso

### 2. Criar uma API Key

1. Na página do Google AI Studio, clique em **"Create API Key"**
2. Escolha um projeto existente ou crie um novo
3. A chave será gerada automaticamente
4. **IMPORTANTE**: Copie a chave imediatamente e guarde em local seguro

### 3. Configurar no Projeto

1. No diretório do projeto, copie o arquivo `.env.example` para `.env`:
   ```bash
   cp .env.example .env
   ```

2. Abra o arquivo `.env` e substitua `demo_key_replace_with_real_key` pela sua chave:
   ```
   VITE_GOOGLE_API_KEY=sua_chave_aqui
   ```

### 4. Testar a Configuração

1. Reinicie o servidor de desenvolvimento:
   ```bash
   npm run dev
   ```

2. Complete o teste até o final
3. Se a análise com IA funcionar, você verá o badge "Análise gerada por IA" nos resultados

## 🆓 Limites Gratuitos

A API do Google Gemini oferece:
- **60 requisições por minuto**
- **1.500 requisições por dia**
- **Totalmente gratuito** para uso pessoal

## 🔒 Segurança

- **Nunca compartilhe** sua API key
- **Não commite** o arquivo `.env` no Git
- A chave é usada apenas no frontend (sem servidor)
- Os dados são processados pela Google de forma segura

## 🚨 Troubleshooting

### Erro: "API key not valid"
- Verifique se copiou a chave corretamente
- Certifique-se de que não há espaços extras
- Tente gerar uma nova chave

### Erro: "Quota exceeded"
- Você atingiu o limite diário
- Aguarde 24 horas ou upgrade para plano pago

### Análise não funciona
- Verifique o console do navegador (F12)
- A aplicação usa análise local como fallback
- Mesmo sem IA, o teste funciona normalmente

## 🌐 Deploy em Produção

### Vercel
1. No dashboard da Vercel, vá em Settings > Environment Variables
2. Adicione: `VITE_GOOGLE_API_KEY` = `sua_chave`

### Netlify
1. No dashboard do Netlify, vá em Site Settings > Environment Variables
2. Adicione: `VITE_GOOGLE_API_KEY` = `sua_chave`

## 📞 Suporte

Se você tiver problemas:
1. Verifique se seguiu todos os passos
2. Teste primeiro em desenvolvimento local
3. Consulte a documentação oficial: https://ai.google.dev/
4. Abra uma issue no GitHub com detalhes do erro

---

**Lembre-se**: A aplicação funciona perfeitamente mesmo sem a API do Google, usando análise local inteligente!
