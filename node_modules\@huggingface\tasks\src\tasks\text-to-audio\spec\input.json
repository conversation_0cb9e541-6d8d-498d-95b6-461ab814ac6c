{"$id": "/inference/schemas/text-to-audio/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Text To Audio inference", "title": "TextToAudioInput", "type": "object", "properties": {"inputs": {"description": "The input text data", "type": "string"}, "parameters": {"description": "Additional inference parameters", "$ref": "#/$defs/TextToAudioParameters"}}, "$defs": {"TextToAudioParameters": {"title": "TextToAudioParameters", "description": "Additional inference parameters for Text To Audio", "type": "object", "properties": {"generation_parameters": {"description": "Parametrization of the text generation process", "$ref": "/inference/schemas/common-definitions.json#/definitions/GenerationParameters"}}}}, "required": ["inputs"]}