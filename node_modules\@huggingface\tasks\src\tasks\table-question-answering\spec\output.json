{"$id": "/inference/schemas/table-question-answering/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Table Question Answering task", "title": "TableQuestionAnsweringOutput", "type": "array", "items": {"type": "object", "properties": {"answer": {"type": "string", "description": "The answer of the question given the table. If there is an aggregator, the answer will be preceded by `AGGREGATOR >`."}, "coordinates": {"type": "array", "description": "Coordinates of the cells of the answers.", "items": {"type": "array", "items": {"type": "integer"}, "minLength": 2, "maxLength": 2}}, "cells": {"type": "array", "description": "List of strings made up of the answer cell values.", "items": {"type": "string"}}, "aggregator": {"type": "string", "description": "If the model has an aggregator, this returns the aggregator."}}, "required": ["answer", "cells", "coordinates"]}}