# 🧠 Teste de Autodescoberta com IA

Uma aplicação web interativa para autodescoberta pessoal que utiliza Inteligência Artificial (Google Gemini) para gerar análises personalizadas, planos de melhoria e feedback sobre hábitos.

## ✨ Funcionalidades

- **🎯 Teste Interativo**: 6 seções com 18 perguntas reflexivas (TODAS OBRIGATÓRIAS)
- **🤖 Análise 100% Real com IA**: Integração exclusiva com Google Gemini
- **📊 Relatório Completo**: Pontuações, forças, sabotadores e missão pessoal
- **📅 Plano de Melhoria**: Estratégias para curto, médio e longo prazo
- **🔄 Análise de Hábitos**: Identificação de padrões positivos e negativos
- **💾 Persistência Local**: Salva progresso automaticamente
- **📱 Design Responsivo**: Funciona em desktop e mobile
- **📄 Export CSV**: Exporta resultados para análise externa
- **🚫 Sem Análises Falsas**: Apenas resultados genuínos da IA

## 🚀 Como Configurar

### 1. Instalação

```bash
# Clone o repositório
git clone <url-do-repositorio>
cd autodescoberta-app

# Instale as dependências
npm install
```

### 2. Configuração da API Google Gemini

1. **Obtenha uma API Key gratuita**:
   - Acesse [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Faça login com sua conta Google
   - Clique em "Create API Key"
   - Copie a chave gerada

2. **Configure as variáveis de ambiente**:
   ```bash
   # Copie o arquivo de exemplo
   cp .env.example .env
   
   # Edite o arquivo .env e adicione sua API key
   VITE_GOOGLE_API_KEY=sua_api_key_aqui
   ```

### 3. Executar a Aplicação

```bash
# Modo desenvolvimento
npm run dev

# Build para produção
npm run build

# Preview da build
npm run preview
```

## 🔧 Tecnologias Utilizadas

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **IA**: Google Gemini API
- **Ícones**: Lucide React
- **Notificações**: React Hot Toast
- **Estado**: React Context API

## 📋 Estrutura do Projeto

```
src/
├── components/          # Componentes React
│   ├── Welcome.tsx     # Tela de boas-vindas
│   ├── SectionForm.tsx # Formulário das seções
│   ├── AnalysisLoading.tsx # Tela de carregamento da análise
│   └── Results.tsx     # Exibição dos resultados
├── context/            # Context API para estado global
├── data/              # Dados das seções e perguntas
├── services/          # Integração com APIs externas
├── types/             # Definições TypeScript
├── utils/             # Utilitários e análise local
└── styles/            # Estilos CSS customizados
```

## 🎨 Seções do Teste

1. **🎯 Zona de Genialidade**: Identifica talentos únicos e habilidades naturais
2. **⚡ Drena Energia**: Reconhece atividades e situações que esgotam
3. **🌊 Rotina de Flow**: Descobre condições ideais para alta performance
4. **🚀 Habilidades Exponenciais**: Mapeia competências multiplicadoras
5. **🚫 Sabotadores Inconscientes**: Identifica padrões limitantes
6. **🎭 Missão e Visão**: Define propósito e direção de vida

## 🤖 Análise com IA

A aplicação utiliza o Google Gemini para:

- **Análise Semântica**: Compreende o contexto das respostas
- **Identificação de Padrões**: Reconhece temas recorrentes
- **Geração de Insights**: Cria análises personalizadas
- **Planos Acionáveis**: Desenvolve estratégias específicas
- **Feedback Inteligente**: Oferece sugestões baseadas em dados

### ⚠️ IMPORTANTE: Apenas Análise Real

**Esta aplicação NÃO fornece análises falsas ou genéricas.**

- ✅ **Análise Real**: Apenas com Google Gemini AI configurado
- ❌ **Sem Fallback**: Não há análise local falsa
- 🔒 **Qualidade Garantida**: Resultados genuínos ou nenhum resultado
- 📝 **Respostas Obrigatórias**: Todas as 18 perguntas devem ser respondidas (mín. 10 caracteres)

## 🔒 Privacidade e Segurança

- **Dados Locais**: Informações salvas apenas no navegador
- **API Segura**: Comunicação criptografada com Google
- **Sem Armazenamento**: Não mantemos dados em servidores
- **Controle Total**: Usuário pode limpar dados a qualquer momento

## 📊 Resultados Gerados

### Pontuações (0-100)
- **Zona de Genialidade**: Clareza sobre talentos únicos
- **Energia Positiva**: Nível de alinhamento energético
- **Clareza**: Compreensão sobre direção pessoal
- **Alinhamento**: Coerência entre valores e ações

### Análises Detalhadas
- **Principais Forças**: 3-5 competências destacadas
- **Sabotadores**: Padrões limitantes identificados
- **Missão Pessoal**: Propósito de vida personalizado
- **Plano de Melhoria**: Ações para 3 horizontes temporais
- **Feedback de Hábitos**: Análise comportamental completa

## 🌐 Deploy

### Vercel (Recomendado)
```bash
# Instale a CLI da Vercel
npm i -g vercel

# Deploy
vercel

# Configure as variáveis de ambiente no dashboard da Vercel
```

### Netlify
```bash
# Build
npm run build

# Upload da pasta dist/ para Netlify
# Configure VITE_GOOGLE_API_KEY nas variáveis de ambiente
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

Se você encontrar problemas:

1. **Verifique a API Key**: Certifique-se de que está configurada corretamente
2. **Console do Navegador**: Verifique erros no console
3. **Fallback Local**: A aplicação funciona mesmo sem IA
4. **Issues**: Abra uma issue no GitHub com detalhes do problema

## 🎯 Próximas Funcionalidades

- [ ] Integração com outras IAs (OpenAI, Claude)
- [ ] Sistema de usuários e histórico
- [ ] Comparação de resultados ao longo do tempo
- [ ] Relatórios em PDF
- [ ] Integração com calendário para lembretes
- [ ] Comunidade para compartilhamento de insights

---

**Desenvolvido com ❤️ para ajudar pessoas a descobrirem seu potencial único**
