import { Brain, Spark<PERSON>, Target, TrendingUp } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useEffect, useState } from 'react';
import { ConfigurationError } from './ConfigurationError';

export function AnalysisLoading() {
  const { completeForm } = useApp();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Inicia a análise automaticamente quando o componente monta
    const startAnalysis = async () => {
      try {
        await completeForm();
      } catch (err: any) {
        setError(err.message || 'Erro desconhecido na análise');
      }
    };

    startAnalysis();
  }, [completeForm]);

  // Se houver erro, mostra a tela de configuração
  if (error) {
    return <ConfigurationError error={error} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animação principal */}
        <div className="relative mb-8">
          <div className="w-32 h-32 mx-auto relative">
            {/* Círculo externo rotativo */}
            <div className="absolute inset-0 border-4 border-primary-200 rounded-full animate-spin">
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                <div className="w-4 h-4 bg-primary-600 rounded-full"></div>
              </div>
            </div>
            
            {/* Círculo interno */}
            <div className="absolute inset-4 border-4 border-secondary-200 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '3s' }}>
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                <div className="w-3 h-3 bg-secondary-600 rounded-full"></div>
              </div>
            </div>
            
            {/* Ícone central */}
            <div className="absolute inset-0 flex items-center justify-center">
              <Brain className="w-12 h-12 text-primary-600 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Título e descrição */}
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
          🤖 Analisando suas Respostas
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Nossa IA está processando suas respostas para criar um perfil personalizado e um plano de desenvolvimento único para você.
        </p>

        {/* Etapas do processo */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Identificando Padrões
            </h3>
            <p className="text-gray-600 text-sm">
              Analisando suas respostas para identificar forças únicas e padrões comportamentais
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Target className="w-6 h-6 text-secondary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Definindo Missão
            </h3>
            <p className="text-gray-600 text-sm">
              Criando uma missão pessoal alinhada com seus valores e aspirações
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Plano de Melhoria
            </h3>
            <p className="text-gray-600 text-sm">
              Desenvolvendo estratégias personalizadas para curto, médio e longo prazo
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Brain className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Feedback Inteligente
            </h3>
            <p className="text-gray-600 text-sm">
              Gerando insights sobre hábitos e recomendações para transformação pessoal
            </p>
          </div>
        </div>

        {/* Mensagem de progresso */}
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <p className="text-gray-700 font-medium">
            Processando com Inteligência Artificial...
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Este processo pode levar alguns segundos para garantir a melhor qualidade de análise.
          </p>
        </div>

        {/* Informação sobre a tecnologia */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            ⚡ Powered by Google Gemini AI • 🔒 Seus dados são processados com segurança
          </p>
        </div>
      </div>
    </div>
  );
}
