{"$id": "/inference/schemas/zero-shot-classification/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Zero Shot Classification inference", "title": "ZeroShotClassificationInput", "type": "object", "properties": {"inputs": {"description": "The input text data, with candidate labels", "type": "object", "title": "ZeroShotClassificationInputData", "properties": {"text": {"type": "string", "description": "The text to classify"}, "candidateLabels": {"type": "array", "description": "The set of possible class labels to classify the text into.", "items": {"type": "string"}}}, "required": ["text", "<PERSON><PERSON><PERSON><PERSON>"]}, "parameters": {"description": "Additional inference parameters", "$ref": "#/$defs/ZeroShotClassificationParameters"}}, "$defs": {"ZeroShotClassificationParameters": {"title": "ZeroShotClassificationParameters", "description": "Additional inference parameters for Zero Shot Classification", "type": "object", "properties": {"hypothesis_template": {"type": "string", "description": "The sentence used in conjunction with candidate<PERSON><PERSON><PERSON> to attempt the text classification by replacing the placeholder with the candidate labels."}, "multi_label": {"type": "boolean", "description": "Whether multiple candidate labels can be true. If false, the scores are normalized such that the sum of the label likelihoods for each sequence is 1. If true, the labels are considered independent and probabilities are normalized for each candidate."}}}}, "required": ["inputs"]}