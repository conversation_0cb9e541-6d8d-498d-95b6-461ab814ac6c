import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { AppState, FormData } from '../types';
import { analyzeFeedbackWithAI } from '../utils/feedbackAnalyzer';
import { getAIProviderInfo } from '../services/aiService';
import toast from 'react-hot-toast';

type AppAction =
  | { type: 'SET_CURRENT_STEP'; payload: number }
  | { type: 'UPDATE_FORM_DATA'; payload: Partial<FormData> }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' }
  | { type: 'START_ANALYSIS' }
  | { type: 'COMPLETE_FORM'; payload: any }
  | { type: 'RESET_FORM' }
  | { type: 'LOAD_FROM_STORAGE'; payload: AppState };

const initialState: AppState = {
  currentStep: 0,
  formData: {},
  isCompleted: false,
  feedback: undefined
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_CURRENT_STEP':
      return { ...state, currentStep: action.payload };
    
    case 'UPDATE_FORM_DATA':
      const updatedFormData = { ...state.formData, ...action.payload };
      // Salva no localStorage
      localStorage.setItem('autodescoberta-form', JSON.stringify(updatedFormData));
      return { ...state, formData: updatedFormData };
    
    case 'NEXT_STEP':
      return { ...state, currentStep: state.currentStep + 1 };
    
    case 'PREV_STEP':
      return { ...state, currentStep: Math.max(0, state.currentStep - 1) };
    
    case 'START_ANALYSIS':
      return { ...state, currentStep: state.currentStep + 1 };

    case 'COMPLETE_FORM':
      // Salva o resultado completo
      const completedState = { ...state, isCompleted: true, feedback: action.payload };
      localStorage.setItem('autodescoberta-result', JSON.stringify(completedState));
      return completedState;
    
    case 'RESET_FORM':
      localStorage.removeItem('autodescoberta-form');
      localStorage.removeItem('autodescoberta-result');
      return initialState;
    
    case 'LOAD_FROM_STORAGE':
      return action.payload;
    
    default:
      return state;
  }
}

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
  startAnalysis: () => void;
  completeForm: () => Promise<void>;
  resetForm: () => void;
  exportToCsv: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Carrega dados do localStorage na inicialização
  React.useEffect(() => {
    const savedResult = localStorage.getItem('autodescoberta-result');
    const savedForm = localStorage.getItem('autodescoberta-form');
    
    if (savedResult) {
      try {
        const parsedResult = JSON.parse(savedResult);
        dispatch({ type: 'LOAD_FROM_STORAGE', payload: parsedResult });
      } catch (error) {
        console.error('Erro ao carregar resultado salvo:', error);
      }
    } else if (savedForm) {
      try {
        const parsedForm = JSON.parse(savedForm);
        dispatch({ type: 'UPDATE_FORM_DATA', payload: parsedForm });
      } catch (error) {
        console.error('Erro ao carregar formulário salvo:', error);
      }
    }
  }, []);

  const updateFormData = (data: Partial<FormData>) => {
    dispatch({ type: 'UPDATE_FORM_DATA', payload: data });
  };

  const nextStep = () => {
    dispatch({ type: 'NEXT_STEP' });
  };

  const prevStep = () => {
    dispatch({ type: 'PREV_STEP' });
  };

  const startAnalysis = () => {
    dispatch({ type: 'START_ANALYSIS' });
  };

  const completeForm = async () => {
    try {
      const aiInfo = getAIProviderInfo();
      toast.loading(`Analisando com ${aiInfo.name}...`, { id: 'analysis' });

      const feedback = await analyzeFeedbackWithAI(state.formData as FormData);

      toast.success(`Análise concluída com ${aiInfo.name}!`, { id: 'analysis' });
      dispatch({ type: 'COMPLETE_FORM', payload: feedback });
    } catch (error: any) {
      console.error('Erro na análise:', error);

      toast.error('Erro na análise. Tente novamente.', {
        id: 'analysis',
        duration: 4000
      });

      // Volta para a seção anterior em caso de erro
      dispatch({ type: 'PREV_STEP' });
    }
  };

  const resetForm = () => {
    dispatch({ type: 'RESET_FORM' });
  };

  const exportToCsv = () => {
    if (!state.formData) return;

    const csvData = Object.entries(state.formData)
      .map(([key, value]) => `"${key}","${value?.replace(/"/g, '""') || ''}"`)
      .join('\n');
    
    const csvContent = 'data:text/csv;charset=utf-8,Campo,Resposta\n' + csvData;
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `autodescoberta-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const value: AppContextType = {
    state,
    dispatch,
    updateFormData,
    nextStep,
    prevStep,
    startAnalysis,
    completeForm,
    resetForm,
    exportToCsv
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
