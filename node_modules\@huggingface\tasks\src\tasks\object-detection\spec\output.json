{"$id": "/inference/schemas/object-detection/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Object Detection task", "title": "ObjectDetectionOutput", "type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "The predicted label for the bounding box."}, "score": {"type": "number", "description": "The associated score / probability."}, "box": {"$ref": "#/$defs/BoundingBox", "description": "The predicted bounding box. Coordinates are relative to the top left corner of the input image."}}, "required": ["box", "label", "score"]}, "$defs": {"BoundingBox": {"type": "object", "title": "BoundingBox", "properties": {"xmin": {"type": "integer", "description": "The x-coordinate of the top-left corner of the bounding box."}, "xmax": {"type": "integer", "description": "The x-coordinate of the bottom-right corner of the bounding box."}, "ymin": {"type": "integer", "description": "The y-coordinate of the top-left corner of the bounding box."}, "ymax": {"type": "integer", "description": "The y-coordinate of the bottom-right corner of the bounding box."}}, "required": ["xmin", "xmax", "ymin", "ymax"]}}}