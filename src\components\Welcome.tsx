import { Play, Target, Lightbulb, Heart } from 'lucide-react';
import { useApp } from '../context/AppContext';

export function Welcome() {
  const { nextStep } = useApp();

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
      <div className="max-w-4xl mx-auto text-center">
        <div className="mb-8">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
            Teste de <span className="text-primary-600">Autodescoberta</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8">
            Descubra sua zona de genialidade, elimine sabotadores e alinhe sua missão de vida
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Target className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Zona de Genialidade
            </h3>
            <p className="text-gray-600 text-sm">
              Identifique seus talentos únicos e habilidades naturais que te destacam
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Lightbulb className="w-6 h-6 text-secondary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Elimine Sabotadores
            </h3>
            <p className="text-gray-600 text-sm">
              Reconheça e supere os padrões que limitam seu potencial
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Heart className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Missão de Vida
            </h3>
            <p className="text-gray-600 text-sm">
              Defina seu propósito e a direção que quer seguir
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg p-8 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Como funciona?
          </h2>
          <div className="grid md:grid-cols-4 gap-4 text-left">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                1
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Responda</h4>
                <p className="text-sm text-gray-600">18 perguntas reflexivas em 6 seções</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                2
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Analise</h4>
                <p className="text-sm text-gray-600">Sistema inteligente processa suas respostas</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                3
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Receba</h4>
                <p className="text-sm text-gray-600">Feedback personalizado e recomendações</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                4
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Aplique</h4>
                <p className="text-sm text-gray-600">Use os insights para transformar sua vida</p>
              </div>
            </div>
          </div>
        </div>

        {/* Aviso sobre API */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mt-1">
              ⚡
            </div>
            <div>
              <h3 className="font-semibold text-blue-800 mb-2">
                Análise Powered by Google Gemini AI
              </h3>
              <p className="text-blue-700 text-sm mb-3">
                Este teste utiliza Inteligência Artificial real para gerar análises personalizadas.
                É necessário configurar uma API key gratuita do Google Gemini.
              </p>
              <div className="flex items-center space-x-4 text-xs text-blue-600">
                <span>✅ 100% Gratuito</span>
                <span>✅ Análise Real com IA</span>
                <span>✅ Sem Resultados Falsos</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <button
            onClick={nextStep}
            className="inline-flex items-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
          >
            <Play className="w-5 h-5 mr-2" />
            Começar Teste
          </button>
          <p className="text-sm text-gray-500 mt-4">
            ⏱️ Tempo estimado: 15-20 minutos • 🤖 Análise com IA real
          </p>
        </div>
      </div>
    </div>
  );
}
