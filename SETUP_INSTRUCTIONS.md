# 🚀 Instruções de Configuração - Teste de Autodescoberta

## ⚠️ IMPORTANTE: Configuração Obrigatória

Esta aplicação **NÃO funciona sem a configuração da API do Google Gemini**. Não fornecemos análises falsas ou genéricas.

## 📋 Pré-requisitos

- Node.js 16+ instalado
- Conta Google (Gmail)
- 5 minutos para configuração

## 🔧 Passo a Passo

### 1. Instalar Dependências

```bash
npm install
```

### 2. Obter API Key do Google Gemini (GRATUITA)

1. **Acesse**: https://makersuite.google.com/app/apikey
2. **Faça login** com sua conta Google
3. **Clique em "Create API Key"**
4. **Copie a chave** gerada (guarde em local seguro)

### 3. Configurar Variáveis de Ambiente

1. **Copie o arquivo de exemplo**:
   ```bash
   cp .env.example .env
   ```

2. **Edite o arquivo `.env`**:
   ```
   VITE_GOOGLE_API_KEY=sua_chave_aqui
   ```

### 4. Executar a Aplicação

```bash
# Desenvolvimento
npm run dev

# Produção
npm run build
npm run preview
```

## ✅ Verificação

1. Acesse http://localhost:5173
2. Complete o teste até o final
3. Se ver "Análise gerada por IA" nos resultados = ✅ Configurado corretamente
4. Se aparecer erro de configuração = ❌ Verifique a API key

## 🆓 Limites Gratuitos

- **60 requisições/minuto**
- **1.500 requisições/dia**
- **Totalmente gratuito** para uso pessoal

## 🔒 Funcionalidades Implementadas

### ✅ Validações Rigorosas
- **Todas as 18 perguntas são obrigatórias**
- **Mínimo 10 caracteres por resposta**
- **Validação visual em tempo real**
- **Botão bloqueado até completar seção**

### ✅ Análise 100% Real
- **Apenas Google Gemini AI**
- **Sem análises falsas ou genéricas**
- **Erro claro se API não configurada**
- **Instruções de configuração integradas**

### ✅ Resultados Completos
- **Principais forças identificadas**
- **Sabotadores inconscientes**
- **Missão pessoal personalizada**
- **Plano de melhoria (curto/médio/longo prazo)**
- **Análise de hábitos positivos/negativos**
- **Sugestões de mudança específicas**
- **Pontuações detalhadas**

## 🚨 Troubleshooting

### Erro: "API key não configurada"
- Verifique se o arquivo `.env` existe
- Confirme se a chave está correta
- Reinicie o servidor (`Ctrl+C` e `npm run dev`)

### Erro: "Quota exceeded"
- Aguarde 24 horas (limite diário atingido)
- Ou crie nova API key

### Análise não funciona
- Verifique conexão com internet
- Confirme se a API key é válida
- Veja o console do navegador (F12) para erros

## 🌐 Deploy em Produção

### Vercel
1. Conecte seu repositório
2. Configure `VITE_GOOGLE_API_KEY` nas variáveis de ambiente
3. Deploy automático

### Netlify
1. Faça upload da pasta `dist/` após `npm run build`
2. Configure `VITE_GOOGLE_API_KEY` nas variáveis de ambiente

## 📊 Estrutura de Dados

### Entrada (FormData)
- 18 campos obrigatórios
- Mínimo 10 caracteres cada
- Validação em tempo real

### Saída (FeedbackResult)
```typescript
{
  principaisForças: string[];
  sabotadoresEvidentes: string[];
  missaoPessoal: string;
  planoMelhoria: {
    curto_prazo: string[];
    medio_prazo: string[];
    longo_prazo: string[];
  };
  feedbackHabitos: {
    habitos_positivos: string[];
    habitos_negativos: string[];
    sugestoes_mudanca: string[];
  };
  pontuacao: {
    zonaGenialidade: number;
    energiaPositiva: number;
    clareza: number;
    alinhamento: number;
  };
  recomendacoes: string[];
  isAIGenerated: true;
}
```

## 🎯 Próximos Passos

1. **Configure a API** seguindo este guia
2. **Teste localmente** para garantir funcionamento
3. **Deploy em produção** com variáveis de ambiente
4. **Compartilhe** com usuários finais

## 📞 Suporte

- **Documentação**: README.md
- **Configuração API**: GOOGLE_API_SETUP.md
- **Issues**: GitHub Issues

---

**Lembre-se**: Esta aplicação prioriza qualidade sobre conveniência. Preferimos não fornecer resultados a fornecer resultados falsos.
