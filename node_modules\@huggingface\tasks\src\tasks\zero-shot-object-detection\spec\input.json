{"$id": "/inference/schemas/zero-shot-object-detection/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Zero Shot Object Detection inference", "title": "ZeroShotObjectDetectionInput", "type": "object", "properties": {"inputs": {"description": "The input image data, with candidate labels", "type": "object", "title": "ZeroShotObjectDetectionInputData", "properties": {"image": {"description": "The image data to generate bounding boxes from"}, "candidateLabels": {"description": "The candidate labels for this image", "type": "array", "items": {"type": "string"}}}, "required": ["image", "<PERSON><PERSON><PERSON><PERSON>"]}, "parameters": {"description": "Additional inference parameters", "$ref": "#/$defs/ZeroShotObjectDetectionParameters"}}, "$defs": {"ZeroShotObjectDetectionParameters": {"title": "ZeroShotObjectDetectionParameters", "description": "Additional inference parameters for Zero Shot Object Detection", "type": "object", "properties": {}}}, "required": ["inputs"]}