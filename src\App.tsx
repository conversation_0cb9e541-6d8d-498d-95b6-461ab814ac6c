import { AppProvider, useApp } from './context/AppContext';
import { Welcome } from './components/Welcome';
import { SectionForm } from './components/SectionForm';
import { Results } from './components/Results';
import { sections } from './data/sections';
import './index.css';

function AppContent() {
  const { state } = useApp();
  const { currentStep, isCompleted } = state;

  // Se o teste foi completado, mostra os resultados
  if (isCompleted) {
    return <Results />;
  }

  // Se está no passo 0, mostra a tela de boas-vindas
  if (currentStep === 0) {
    return <Welcome />;
  }

  // Calcula o índice da seção (currentStep - 1 porque o step 0 é a welcome)
  const sectionIndex = currentStep - 1;
  
  // Verifica se o índice da seção é válido
  if (sectionIndex >= 0 && sectionIndex < sections.length) {
    return <SectionForm sectionIndex={sectionIndex} />;
  }

  // Fallback para casos inesperados
  return <Welcome />;
}

function App() {
  return (
    <AppProvider>
      <div className="App">
        <AppContent />
      </div>
    </AppProvider>
  );
}

export default App;
