import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Save } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { sections } from '../data/sections';
import { FormData } from '../types';

interface SectionFormProps {
  sectionIndex: number;
}

export function SectionForm({ sectionIndex }: SectionFormProps) {
  const { state, updateFormData, nextStep, prevStep, startAnalysis } = useApp();
  const section = sections[sectionIndex];
  const [localData, setLocalData] = useState<Partial<FormData>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Carrega dados existentes quando o componente monta
  useEffect(() => {
    const existingData: Partial<FormData> = {};
    section.questions.forEach(question => {
      existingData[question.id] = state.formData[question.id] || '';
    });
    setLocalData(existingData);
  }, [section, state.formData]);

  const handleInputChange = (questionId: keyof FormData, value: string) => {
    setLocalData(prev => ({ ...prev, [questionId]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    updateFormData(localData);
    setHasChanges(false);
  };

  const handleNext = () => {
    updateFormData(localData);
    if (sectionIndex === sections.length - 1) {
      startAnalysis();
    } else {
      nextStep();
    }
  };

  const handlePrev = () => {
    updateFormData(localData);
    prevStep();
  };

  const isLastSection = sectionIndex === sections.length - 1;
  const progress = ((sectionIndex + 1) / sections.length) * 100;

  // Verifica se todas as perguntas da seção foram respondidas (mínimo 10 caracteres)
  const allQuestionsAnswered = section.questions.every(
    question => localData[question.id]?.trim().length && localData[question.id]!.trim().length >= 10
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header com progresso */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-800">
              Seção {sectionIndex + 1} de {sections.length}
            </h1>
            <div className="text-sm text-gray-600">
              {Math.round(progress)}% concluído
            </div>
          </div>
          
          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Conteúdo da seção */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-6">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              {section.title}
            </h2>
            <p className="text-lg text-gray-600">
              {section.description}
            </p>
          </div>

          {/* Perguntas */}
          <div className="space-y-8">
            {section.questions.map((question, index) => (
              <div key={question.id} className="space-y-3">
                <label className="block">
                  <div className="flex items-start space-x-3 mb-3">
                    <div className="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-semibold mt-1">
                      {index + 1}
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 leading-relaxed">
                      {question.text}
                    </h3>
                  </div>
                  <textarea
                    value={localData[question.id] || ''}
                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                    placeholder={question.placeholder}
                    rows={4}
                    required
                    minLength={10}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none transition-colors duration-200 ${
                      (localData[question.id]?.trim().length || 0) >= 10
                        ? 'border-green-300 bg-green-50'
                        : (localData[question.id]?.trim().length || 0) > 0
                        ? 'border-yellow-300 bg-yellow-50'
                        : 'border-gray-300'
                    }`}
                  />
                  <div className="mt-1 flex justify-between items-center">
                    <span className={`text-xs ${
                      (localData[question.id]?.trim().length || 0) >= 10
                        ? 'text-green-600'
                        : (localData[question.id]?.trim().length || 0) > 0
                        ? 'text-yellow-600'
                        : 'text-gray-500'
                    }`}>
                      {localData[question.id]?.trim().length || 0}/10 caracteres mínimos
                    </span>
                    {(localData[question.id]?.trim().length || 0) >= 10 && (
                      <span className="text-green-600 text-xs">✓ Completo</span>
                    )}
                  </div>
                </label>
              </div>
            ))}
          </div>

          {/* Botão de salvar */}
          {hasChanges && (
            <div className="mt-6 text-center">
              <button
                onClick={handleSave}
                className="inline-flex items-center px-4 py-2 bg-secondary-600 text-white font-medium rounded-lg hover:bg-secondary-700 transition-colors duration-200"
              >
                <Save className="w-4 h-4 mr-2" />
                Salvar Progresso
              </button>
            </div>
          )}
        </div>

        {/* Navegação */}
        <div className="flex justify-between items-center">
          <button
            onClick={handlePrev}
            disabled={sectionIndex === 0}
            className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <ChevronLeft className="w-5 h-5 mr-2" />
            Anterior
          </button>

          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              {allQuestionsAnswered ? '✅ Seção completa' : '❌ Complete todas as perguntas (mín. 10 caracteres cada)'}
            </p>
            {!allQuestionsAnswered && (
              <p className="text-xs text-red-500 font-medium">
                Todas as perguntas são obrigatórias para uma análise precisa
              </p>
            )}
          </div>

          <button
            onClick={handleNext}
            disabled={!allQuestionsAnswered}
            className="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {isLastSection ? 'Analisar com IA' : 'Próxima'}
            <ChevronRight className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
}
