{"$id": "/inference/schemas/image-segmentation/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Image Segmentation task", "title": "ImageSegmentationOutput", "type": "array", "items": {"description": "A predicted mask / segment", "type": "object", "properties": {"label": {"type": "string", "description": "The label of the predicted segment."}, "mask": {"type": "string", "description": "The corresponding mask as a black-and-white image (base64-encoded)."}, "score": {"type": "number", "description": "The score or confidence degree the model has."}}, "required": ["label", "mask"]}}