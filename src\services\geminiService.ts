import { GoogleGenerativeAI } from '@google/generative-ai';
import { FormData } from '../types';

// Configuração da API
const API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;

let genAI: GoogleGenerativeAI | null = null;

// Inicializa o cliente Gemini apenas se a API key estiver disponível
if (API_KEY && API_KEY !== 'demo_key_replace_with_real_key') {
  genAI = new GoogleGenerativeAI(API_KEY);
}

export interface AIAnalysisResult {
  principaisForças: string[];
  sabotadoresEvidentes: string[];
  missaoPessoal: string;
  planoMelhoria: {
    curto_prazo: string[];
    medio_prazo: string[];
    longo_prazo: string[];
  };
  feedbackHabitos: {
    habitos_positivos: string[];
    habitos_negativos: string[];
    sugestoes_mudanca: string[];
  };
  pontuacao: {
    zonaGenialidade: number;
    energiaPositiva: number;
    clareza: number;
    alinhamento: number;
  };
  recomendacoes: string[];
}

function formatFormDataForAI(formData: FormData): string {
  const sections = [
    {
      title: "Zona de Genialidade",
      questions: [
        { q: "O que só você consegue fazer melhor que a média?", a: formData.zona_genialidade_1 },
        { q: "O que as pessoas sempre pedem ajuda para você?", a: formData.zona_genialidade_2 },
        { q: "Atividades onde o tempo passa sem perceber?", a: formData.zona_genialidade_3 },
        { q: "O que considera óbvio mas é complexo para outros?", a: formData.zona_genialidade_4 }
      ]
    },
    {
      title: "O que drena energia",
      questions: [
        { q: "Momentos nadando contra a maré?", a: formData.drena_energia_1 },
        { q: "Compromissos que evita ou procrastina?", a: formData.drena_energia_2 },
        { q: "O que faz por obrigação mas gostaria de largar?", a: formData.drena_energia_3 }
      ]
    },
    {
      title: "Rotina de Flow",
      questions: [
        { q: "Rituais que colocam em alta performance?", a: formData.rotina_flow_1 },
        { q: "Trabalha melhor em ciclos longos ou curtos?", a: formData.rotina_flow_2 },
        { q: "Ambientes que mais inspiram?", a: formData.rotina_flow_3 }
      ]
    },
    {
      title: "Habilidades Exponenciais",
      questions: [
        { q: "Habilidade que desbloquearia várias outras?", a: formData.habilidades_exp_1 },
        { q: "O que seu eu do futuro domina?", a: formData.habilidades_exp_2 },
        { q: "Tendências que vão explodir?", a: formData.habilidades_exp_3 }
      ]
    },
    {
      title: "Sabotadores Inconscientes",
      questions: [
        { q: "Padrões que se repetem e impedem avanço?", a: formData.sabotadores_1 },
        { q: "Teme fracasso ou sucesso?", a: formData.sabotadores_2 },
        { q: "Busca perfeição ou progresso?", a: formData.sabotadores_3 }
      ]
    },
    {
      title: "Missão e Visão",
      questions: [
        { q: "Dia perfeito alinhado com missão?", a: formData.missao_visao_1 },
        { q: "Legado que quer deixar?", a: formData.missao_visao_2 },
        { q: "O que quer que as pessoas sintam após contato?", a: formData.missao_visao_3 }
      ]
    }
  ];

  return sections.map(section => 
    `**${section.title}:**\n` + 
    section.questions.map(q => `- ${q.q}\n  Resposta: ${q.a || 'Não respondido'}`).join('\n')
  ).join('\n\n');
}

const ANALYSIS_PROMPT = `
Você é um especialista em desenvolvimento pessoal e coaching. Analise as respostas do teste de autodescoberta abaixo e forneça um feedback detalhado e personalizado.

INSTRUÇÕES:
1. Identifique as principais forças e talentos únicos da pessoa
2. Reconheça padrões sabotadores e limitantes
3. Sugira uma missão pessoal baseada nas respostas
4. Crie um plano de melhoria estruturado (curto, médio e longo prazo)
5. Analise hábitos positivos e negativos
6. Dê pontuações de 0-100 para cada área
7. Forneça recomendações práticas e acionáveis

FORMATO DE RESPOSTA (JSON):
{
  "principaisForças": ["força1", "força2", "força3"],
  "sabotadoresEvidentes": ["sabotador1", "sabotador2"],
  "missaoPessoal": "descrição da missão pessoal identificada",
  "planoMelhoria": {
    "curto_prazo": ["ação1", "ação2"],
    "medio_prazo": ["ação1", "ação2"],
    "longo_prazo": ["ação1", "ação2"]
  },
  "feedbackHabitos": {
    "habitos_positivos": ["hábito1", "hábito2"],
    "habitos_negativos": ["hábito1", "hábito2"],
    "sugestoes_mudanca": ["sugestão1", "sugestão2"]
  },
  "pontuacao": {
    "zonaGenialidade": 85,
    "energiaPositiva": 70,
    "clareza": 75,
    "alinhamento": 80
  },
  "recomendacoes": ["recomendação1", "recomendação2", "recomendação3"]
}

RESPOSTAS DO TESTE:
`;

export async function analyzeWithAI(formData: FormData): Promise<AIAnalysisResult> {
  // Se não há API key válida, rejeita a promessa
  if (!genAI) {
    throw new Error('API key do Google Gemini não configurada. Configure VITE_GOOGLE_API_KEY no arquivo .env');
  }

  try {
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    const formattedData = formatFormDataForAI(formData);
    const prompt = ANALYSIS_PROMPT + formattedData;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Tenta extrair JSON da resposta
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const analysisResult = JSON.parse(jsonMatch[0]);

      // Valida se a resposta tem a estrutura esperada
      if (!analysisResult.principaisForças || !analysisResult.missaoPessoal) {
        throw new Error('Resposta da IA incompleta ou inválida');
      }

      return analysisResult;
    } else {
      throw new Error('Formato de resposta inválido da IA');
    }
  } catch (error) {
    console.error('Erro na análise com IA:', error);
    // Não usa fallback - rejeita para forçar configuração correta
    throw error;
  }
}

// Função removida - não fornecemos mais análises falsas
// A aplicação agora exige configuração correta da API do Google Gemini
