{"$id": "/inference/schemas/document-question-answering/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Document Question Answering task", "title": "DocumentQuestionAnsweringOutput", "type": "array", "items": {"type": "object", "properties": {"answer": {"type": "string", "description": "The answer to the question."}, "score": {"type": "number", "description": "The probability associated to the answer."}, "start": {"type": "integer", "description": "The start word index of the answer (in the OCR’d version of the input or provided word boxes)."}, "end": {"type": "integer", "description": "The end word index of the answer (in the OCR’d version of the input or provided word boxes)."}, "words": {"type": "array", "items": {"type": "integer"}, "description": "The index of each word/box pair that is in the answer"}}, "required": ["answer", "score", "start", "end", "words"]}}