{"$id": "/inference/schemas/chat-completion/stream_output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Chat Completion Stream Output.\n\nAuto-generated from TGI specs.\nFor more details, check out https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/scripts/inference-tgi-import.ts.", "title": "ChatCompletionStreamOutput", "type": "object", "required": ["id", "created", "model", "system_fingerprint", "choices"], "properties": {"choices": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionStreamOutputChoice"}}, "created": {"type": "integer", "format": "int64", "example": "1706270978", "minimum": 0}, "id": {"type": "string"}, "model": {"type": "string", "example": "mistralai/Mistral-7B-Instruct-v0.2"}, "system_fingerprint": {"type": "string"}, "usage": {"allOf": [{"$ref": "#/$defs/ChatCompletionStreamOutputUsage"}], "nullable": true}}, "$defs": {"ChatCompletionStreamOutputChoice": {"type": "object", "required": ["index", "delta"], "properties": {"delta": {"$ref": "#/$defs/ChatCompletionStreamOutputDelta"}, "finish_reason": {"type": "string", "nullable": true}, "index": {"type": "integer", "format": "int32", "minimum": 0}, "logprobs": {"allOf": [{"$ref": "#/$defs/ChatCompletionStreamOutputLogprobs"}], "nullable": true}}, "title": "ChatCompletionStreamOutputChoice"}, "ChatCompletionStreamOutputDelta": {"oneOf": [{"$ref": "#/$defs/ChatCompletionStreamOutputTextMessage"}, {"$ref": "#/$defs/ChatCompletionStreamOutputToolCallDelta"}], "title": "ChatCompletionStreamOutputDelta"}, "ChatCompletionStreamOutputTextMessage": {"type": "object", "required": ["role", "content"], "properties": {"content": {"type": "string", "example": "My name is <PERSON> and I"}, "role": {"type": "string", "example": "user"}}, "title": "ChatCompletionStreamOutputTextMessage"}, "ChatCompletionStreamOutputToolCallDelta": {"type": "object", "required": ["role", "tool_calls"], "properties": {"role": {"type": "string", "example": "assistant"}, "tool_calls": {"$ref": "#/$defs/ChatCompletionStreamOutputDeltaToolCall"}}, "title": "ChatCompletionStreamOutputToolCallDelta"}, "ChatCompletionStreamOutputDeltaToolCall": {"type": "object", "required": ["index", "id", "type", "function"], "properties": {"function": {"$ref": "#/$defs/ChatCompletionStreamOutputFunction"}, "id": {"type": "string"}, "index": {"type": "integer", "format": "int32", "minimum": 0}, "type": {"type": "string"}}, "title": "ChatCompletionStreamOutputDeltaToolCall"}, "ChatCompletionStreamOutputFunction": {"type": "object", "required": ["arguments"], "properties": {"arguments": {"type": "string"}, "name": {"type": "string", "nullable": true}}, "title": "ChatCompletionStreamOutputFunction"}, "ChatCompletionStreamOutputLogprobs": {"type": "object", "required": ["content"], "properties": {"content": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionStreamOutputLogprob"}}}, "title": "ChatCompletionStreamOutputLogprobs"}, "ChatCompletionStreamOutputLogprob": {"type": "object", "required": ["token", "logprob", "top_logprobs"], "properties": {"logprob": {"type": "number", "format": "float"}, "token": {"type": "string"}, "top_logprobs": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionStreamOutputTopLogprob"}}}, "title": "ChatCompletionStreamOutputLogprob"}, "ChatCompletionStreamOutputTopLogprob": {"type": "object", "required": ["token", "logprob"], "properties": {"logprob": {"type": "number", "format": "float"}, "token": {"type": "string"}}, "title": "ChatCompletionStreamOutputTopLogprob"}, "ChatCompletionStreamOutputUsage": {"type": "object", "required": ["prompt_tokens", "completion_tokens", "total_tokens"], "properties": {"completion_tokens": {"type": "integer", "format": "int32", "minimum": 0}, "prompt_tokens": {"type": "integer", "format": "int32", "minimum": 0}, "total_tokens": {"type": "integer", "format": "int32", "minimum": 0}}, "title": "ChatCompletionStreamOutputUsage"}}}