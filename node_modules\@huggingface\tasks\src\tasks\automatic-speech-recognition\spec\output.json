{"$id": "/inference/schemas/automatic-speech-recognition/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Automatic Speech Recognition task", "title": "AutomaticSpeechRecognitionOutput", "type": "object", "properties": {"text": {"type": "string", "description": "The recognized text."}, "chunks": {"type": "array", "description": "When returnTimestamps is enabled, chunks contains a list of audio chunks identified by the model.", "items": {"type": "object", "title": "AutomaticSpeechRecognitionOutputChunk", "properties": {"text": {"type": "string", "description": "A chunk of text identified by the model"}, "timestamps": {"type": "array", "description": "The start and end timestamps corresponding with the text", "items": {"type": "number"}, "minLength": 2, "maxLength": 2}}, "required": ["text", "timestamps"]}}}, "required": ["text"]}