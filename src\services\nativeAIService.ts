import { HfInference } from '@huggingface/inference';
import { FormData } from '../types';

// Configuração da API Hugging Face (gratuita)
const HF_TOKEN = import.meta.env.VITE_HUGGING_FACE_TOKEN;

let hf: HfInference | null = null;

// Inicializa o cliente Hugging Face
if (HF_TOKEN && HF_TOKEN !== 'demo_token_replace_with_real_token') {
  hf = new HfInference(HF_TOKEN);
}

export interface AIAnalysisResult {
  principaisForças: string[];
  sabotadoresEvidentes: string[];
  missaoPessoal: string;
  planoMelhoria: {
    curto_prazo: string[];
    medio_prazo: string[];
    longo_prazo: string[];
  };
  feedbackHabitos: {
    habitos_positivos: string[];
    habitos_negativos: string[];
    sugestoes_mudanca: string[];
  };
  pontuacao: {
    zonaGenialidade: number;
    energiaPositiva: number;
    clareza: number;
    alinhamento: number;
  };
  recomendacoes: string[];
}

// Modelo gratuito da Hugging Face para análise de texto
const MODEL_NAME = "microsoft/DialoGPT-medium";

function formatFormDataForAI(formData: FormData): string {
  const sections = [
    {
      title: "Zona de Genialidade",
      questions: [
        { q: "O que só você consegue fazer melhor que a média?", a: formData.zona_genialidade_1 },
        { q: "O que as pessoas sempre pedem ajuda para você?", a: formData.zona_genialidade_2 },
        { q: "Atividades onde o tempo passa sem perceber?", a: formData.zona_genialidade_3 },
        { q: "O que considera óbvio mas é complexo para outros?", a: formData.zona_genialidade_4 }
      ]
    },
    {
      title: "O que drena energia",
      questions: [
        { q: "Momentos nadando contra a maré?", a: formData.drena_energia_1 },
        { q: "Compromissos que evita ou procrastina?", a: formData.drena_energia_2 },
        { q: "O que faz por obrigação mas gostaria de largar?", a: formData.drena_energia_3 }
      ]
    },
    {
      title: "Rotina de Flow",
      questions: [
        { q: "Rituais que colocam em alta performance?", a: formData.rotina_flow_1 },
        { q: "Trabalha melhor em ciclos longos ou curtos?", a: formData.rotina_flow_2 },
        { q: "Ambientes que mais inspiram?", a: formData.rotina_flow_3 }
      ]
    },
    {
      title: "Habilidades Exponenciais",
      questions: [
        { q: "Habilidade que desbloquearia várias outras?", a: formData.habilidades_exp_1 },
        { q: "O que seu eu do futuro domina?", a: formData.habilidades_exp_2 },
        { q: "Tendências que vão explodir?", a: formData.habilidades_exp_3 }
      ]
    },
    {
      title: "Sabotadores Inconscientes",
      questions: [
        { q: "Padrões que se repetem e impedem avanço?", a: formData.sabotadores_1 },
        { q: "Teme fracasso ou sucesso?", a: formData.sabotadores_2 },
        { q: "Busca perfeição ou progresso?", a: formData.sabotadores_3 }
      ]
    },
    {
      title: "Missão e Visão",
      questions: [
        { q: "Dia perfeito alinhado com missão?", a: formData.missao_visao_1 },
        { q: "Legado que quer deixar?", a: formData.missao_visao_2 },
        { q: "O que quer que as pessoas sintam após contato?", a: formData.missao_visao_3 }
      ]
    }
  ];

  return sections.map(section => 
    `${section.title}: ` + 
    section.questions.map(q => `${q.q} ${q.a || 'Não respondido'}`).join(' ')
  ).join(' ');
}

// Análise inteligente baseada em palavras-chave e padrões
function analyzeWithKeywords(formData: FormData): AIAnalysisResult {
  const allText = Object.values(formData).join(' ').toLowerCase();
  
  // Análise de forças baseada em palavras-chave
  const forcasMap = {
    'Comunicação e Liderança': ['comunicação', 'falar', 'apresentar', 'liderar', 'equipe', 'inspirar'],
    'Pensamento Analítico': ['análise', 'dados', 'lógica', 'resolver', 'problema', 'estratégia'],
    'Criatividade e Inovação': ['criativ', 'inovação', 'ideias', 'arte', 'design', 'original'],
    'Organização e Planejamento': ['organiz', 'planej', 'estrutur', 'sistema', 'processo', 'método'],
    'Relacionamento Interpessoal': ['pessoas', 'relacionamento', 'social', 'empatia', 'ajudar', 'servir'],
    'Ensino e Mentoria': ['ensino', 'explicar', 'mentor', 'desenvolver', 'crescimento', 'aprender']
  };

  const principaisForças = Object.entries(forcasMap)
    .filter(([_, keywords]) => keywords.some(keyword => allText.includes(keyword)))
    .map(([forca]) => forca)
    .slice(0, 4);

  // Análise de sabotadores
  const sabotadoresMap = {
    'Perfeccionismo Paralisante': ['perfeccion', 'perfeito', 'detalhes', 'nunca bom'],
    'Procrastinação Crônica': ['procrastin', 'adiar', 'postergar', 'deixar para depois'],
    'Medo do Fracasso': ['medo', 'receio', 'fracasso', 'erro', 'falhar'],
    'Síndrome do Impostor': ['não mereço', 'sorte', 'impostor', 'não sou bom'],
    'Necessidade de Aprovação': ['aprovação', 'agradar', 'crítica', 'julgamento'],
    'Comparação Constante': ['comparação', 'outros', 'melhor que eu', 'inveja']
  };

  const sabotadoresEvidentes = Object.entries(sabotadoresMap)
    .filter(([_, keywords]) => keywords.some(keyword => allText.includes(keyword)))
    .map(([sabotador]) => sabotador)
    .slice(0, 3);

  // Geração de missão baseada em padrões
  let missaoPessoal = '';
  if (allText.includes('ajudar') || allText.includes('servir') || allText.includes('contribuir')) {
    missaoPessoal = 'Sua missão está centrada em servir e contribuir para o bem-estar de outros, utilizando seus talentos para fazer a diferença na vida das pessoas.';
  } else if (allText.includes('criar') || allText.includes('inovar') || allText.includes('transformar')) {
    missaoPessoal = 'Sua missão envolve criação e inovação, transformando ideias em realidade e impactando positivamente o mundo através de soluções criativas.';
  } else if (allText.includes('liderar') || allText.includes('inspirar') || allText.includes('motivar')) {
    missaoPessoal = 'Sua missão é de liderança inspiradora, guiando e motivando pessoas em direção ao crescimento e realização de objetivos significativos.';
  } else if (allText.includes('ensinar') || allText.includes('educar') || allText.includes('desenvolver')) {
    missaoPessoal = 'Sua missão está relacionada ao desenvolvimento humano, compartilhando conhecimento e facilitando o crescimento pessoal e profissional de outros.';
  } else {
    missaoPessoal = 'Sua missão única emerge da combinação de seus talentos naturais com sua paixão por criar impacto positivo e deixar um legado significativo.';
  }

  // Cálculo de pontuações baseado na qualidade das respostas
  const responses = Object.values(formData).filter(Boolean);
  const avgLength = responses.reduce((sum, r) => sum + r.length, 0) / responses.length;
  const totalWords = responses.join(' ').split(' ').length;

  const zonaGenialidade = Math.min(100, Math.max(40, (principaisForças.length * 20) + (avgLength > 80 ? 20 : 10)));
  const energiaPositiva = Math.min(100, Math.max(30, 100 - (sabotadoresEvidentes.length * 20)));
  const clareza = Math.min(100, Math.max(35, avgLength > 120 ? 85 : 65));
  const alinhamento = Math.min(100, Math.max(40, totalWords > 500 ? 80 : 60));

  return {
    principaisForças: principaisForças.length > 0 ? principaisForças : ['Autoconhecimento em Desenvolvimento'],
    sabotadoresEvidentes: sabotadoresEvidentes.length > 0 ? sabotadoresEvidentes : ['Padrões ainda não identificados'],
    missaoPessoal,
    planoMelhoria: {
      curto_prazo: [
        'Estabelecer uma rotina diária de reflexão e autoavaliação (10-15 minutos)',
        'Identificar e eliminar uma atividade específica que drena sua energia',
        'Praticar diariamente uma habilidade identificada como força natural'
      ],
      medio_prazo: [
        'Desenvolver um projeto pessoal que utilize suas principais forças identificadas',
        'Buscar feedback estruturado de pessoas de confiança sobre seu desenvolvimento',
        'Implementar técnicas avançadas de gestão de energia e produtividade pessoal'
      ],
      longo_prazo: [
        'Criar um plano de carreira estratégico alinhado com sua missão pessoal',
        'Desenvolver habilidades de liderança e influência positiva em sua área',
        'Estabelecer um legado pessoal e profissional que reflita seus valores'
      ]
    },
    feedbackHabitos: {
      habitos_positivos: [
        'Busca ativa por autoconhecimento e desenvolvimento pessoal',
        'Disposição para reflexão profunda sobre comportamentos e padrões',
        'Interesse genuíno em melhorar e evoluir continuamente'
      ],
      habitos_negativos: sabotadoresEvidentes.length > 0 ? 
        sabotadoresEvidentes.map(s => `Tendência ao ${s.toLowerCase()}`) :
        ['Possível autocrítica excessiva', 'Tendência à procrastinação em áreas desafiadoras'],
      sugestoes_mudanca: [
        'Implementar técnicas de mindfulness para aumentar consciência no momento presente',
        'Praticar autocompaixão e celebrar pequenas vitórias diárias',
        'Estabelecer limites saudáveis entre trabalho, vida pessoal e desenvolvimento',
        'Criar um sistema de accountability com pessoas de confiança'
      ]
    },
    pontuacao: {
      zonaGenialidade,
      energiaPositiva,
      clareza,
      alinhamento
    },
    recomendacoes: [
      'Continue investindo em autoconhecimento através de ferramentas como esta',
      'Considere trabalhar com um coach ou mentor para acelerar seu desenvolvimento',
      'Mantenha um diário de reflexões para acompanhar sua evolução ao longo do tempo',
      'Busque oportunidades que alinhem suas forças naturais com seus valores pessoais',
      'Pratique técnicas de desenvolvimento pessoal adequadas ao seu perfil identificado'
    ]
  };
}

export async function analyzeWithNativeAI(formData: FormData): Promise<AIAnalysisResult> {
  // Sempre usa análise inteligente baseada em padrões (não requer API externa)
  return analyzeWithKeywords(formData);
}

// Função alternativa com Hugging Face (se token estiver configurado)
export async function analyzeWithHuggingFace(formData: FormData): Promise<AIAnalysisResult> {
  if (!hf) {
    // Fallback para análise nativa se não houver token
    return analyzeWithNativeAI(formData);
  }

  try {
    const prompt = formatFormDataForAI(formData);
    
    // Usa modelo gratuito da Hugging Face
    const response = await hf.textGeneration({
      model: MODEL_NAME,
      inputs: `Analyze this self-discovery assessment and provide insights: ${prompt.substring(0, 500)}`,
      parameters: {
        max_new_tokens: 200,
        temperature: 0.7,
        return_full_text: false
      }
    });

    // Combina resposta da IA com análise estruturada
    const baseAnalysis = analyzeWithKeywords(formData);
    
    // Enriquece a missão pessoal com insights da IA
    if (response.generated_text && response.generated_text.length > 50) {
      baseAnalysis.missaoPessoal = `${baseAnalysis.missaoPessoal} ${response.generated_text.substring(0, 200)}`;
    }

    return baseAnalysis;
  } catch (error) {
    console.warn('Erro na API Hugging Face, usando análise nativa:', error);
    return analyzeWithNativeAI(formData);
  }
}
