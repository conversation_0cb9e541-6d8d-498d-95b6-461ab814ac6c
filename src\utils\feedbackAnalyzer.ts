import { FormData, FeedbackResult } from '../types';
import { analyzeWithAI } from '../services/geminiService';

// Todas as funções de análise local foram removidas
// A aplicação agora utiliza exclusivamente a API do Google Gemini

// Todas as funções de análise local foram removidas para garantir apenas análises reais com IA

// Função para análise com IA (assíncrona) - SEM FALLBACK
export async function analyzeFeedbackWithAI(formData: FormData): Promise<FeedbackResult> {
  const aiResult = await analyzeWithAI(formData);
  return {
    ...aiResult,
    isAIGenerated: true
  };
}

// Funções de análise local removidas - apenas análise real com IA é permitida
// Para usar a aplicação, configure a API key do Google Gemini
