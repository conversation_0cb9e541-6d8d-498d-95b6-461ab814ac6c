## Use Cases

### Autonomous Driving

Object Detection is widely used in computer vision for autonomous driving. Self-driving cars use Object Detection models to detect pedestrians, bicycles, traffic lights and road signs to decide which step to take.

### Object Tracking in Matches

Object Detection models are widely used in sports where the ball or a player is tracked for monitoring and refereeing during matches.

### Image Search

Object Detection models are widely used in image search. Smartphones use Object Detection models to detect entities (such as specific places or objects) and allow the user to search for the entity on the Internet.

### Object Counting

Object Detection models are used to count instances of objects in a given image, this can include counting the objects in warehouses or stores, or counting the number of visitors in a store. They are also used to manage crowds at events to prevent disasters.

## Inference

You can infer with Object Detection models through the `object-detection` pipeline. When calling the pipeline you just need to specify a path or http link to an image.

```python
model = pipeline("object-detection")

model("path_to_cat_image")

# [{'label': 'blanket',
#  'mask': mask_string,
#  'score': 0.917},
#...]
```

# Useful Resources

- [Walkthrough of Computer Vision Ecosystem in Hugging Face - CV Study Group](https://www.youtube.com/watch?v=oL-xmufhZM8)
- [Object detection task guide](https://huggingface.co/docs/transformers/tasks/object_detection)
