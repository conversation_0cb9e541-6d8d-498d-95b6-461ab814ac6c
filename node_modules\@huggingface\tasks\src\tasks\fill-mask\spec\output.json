{"$id": "/inference/schemas/fill-mask/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Fill Mask task", "title": "FillMaskOutput", "type": "array", "items": {"type": "object", "properties": {"sequence": {"type": "string", "description": "The corresponding input with the mask token prediction."}, "score": {"type": "number", "description": "The corresponding probability"}, "token": {"type": "integer", "description": "The predicted token id (to replace the masked one)."}, "token_str": {"type": "string", "description": "The predicted token (to replace the masked one)."}}, "required": ["sequence", "score", "token", "tokenStr"]}}