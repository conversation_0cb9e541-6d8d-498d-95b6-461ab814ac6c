{"$id": "/inference/schemas/table-question-answering/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Table Question Answering inference", "title": "TableQuestionAnsweringInput", "type": "object", "properties": {"inputs": {"description": "One (table, question) pair to answer", "title": "TableQuestionAnsweringInputData", "type": "object", "properties": {"table": {"description": "The table to serve as context for the questions", "type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "question": {"description": "The question to be answered about the table", "type": "string"}}, "required": ["table", "question"]}, "parameters": {"description": "Additional inference parameters", "$ref": "#/$defs/TableQuestionAnsweringParameters"}}, "$defs": {"TableQuestionAnsweringParameters": {"title": "TableQuestionAnsweringParameters", "description": "Additional inference parameters for Table Question Answering", "type": "object", "properties": {}}}, "required": ["inputs"]}