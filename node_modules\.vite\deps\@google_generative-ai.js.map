{"version": 3, "sources": ["../../@google/generative-ai/dist/index.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    // Threshold is unspecified.\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    // Content with NEGLIGIBLE will be allowed.\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    // Content with NEGLIGIBLE and LOW will be allowed.\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    // All content will be allowed.\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    // Probability is unspecified.\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    // Content has a negligible chance of being unsafe.\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    // Content has a low chance of being unsafe.\n    HarmProbability[\"LOW\"] = \"LOW\";\n    // Content has a medium chance of being unsafe.\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    // Content has a high chance of being unsafe.\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst API_VERSION = \"v1\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.2.1\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n    }\n    toString() {\n        let url = `${BASE_URL}/${API_VERSION}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders() {\n    return `${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`;\n}\nasync function makeRequest(url, body, requestOptions) {\n    let response;\n    try {\n        response = await fetch(url.toString(), Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: {\n                \"Content-Type\": \"application/json\",\n                \"x-goog-api-client\": getClientHeaders(),\n                \"x-goog-api-key\": url.apiKey,\n            }, body }));\n        if (!response.ok) {\n            let message = \"\";\n            try {\n                const json = await response.json();\n                message = json.error.message;\n                if (json.error.details) {\n                    message += ` ${JSON.stringify(json.error.details)}`;\n                }\n            }\n            catch (e) {\n                // ignored\n            }\n            throw new Error(`[${response.status} ${response.statusText}] ${message}`);\n        }\n    }\n    catch (e) {\n        const err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n        throw err;\n    }\n    return response;\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const abortController = new AbortController();\n        const signal = abortController.signal;\n        setTimeout(() => abortController.abort(), requestOptions.timeout);\n        fetchOptions.signal = signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    return response;\n}\n/**\n * Returns text of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    if ((_d = (_c = (_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) === null || _c === void 0 ? void 0 : _c[0]) === null || _d === void 0 ? void 0 : _d.text) {\n        return response.candidates[0].content.parts[0].text;\n    }\n    else {\n        return \"\";\n    }\n}\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader.read().then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            for (const candidate of response.candidates) {\n                const i = candidate.index;\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[i]) {\n                    aggregatedResponse.candidates[i] = {\n                        index: candidate.index,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[i].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n                aggregatedResponse.candidates[i].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[i].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[i].content) {\n                        aggregatedResponse.candidates[i].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [{ text: \"\" }],\n                        };\n                    }\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            aggregatedResponse.candidates[i].content.parts[0].text +=\n                                part.text;\n                        }\n                    }\n                }\n            }\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const url = new RequestUrl(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true);\n    const response = await makeRequest(url, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const url = new RequestUrl(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false);\n    const response = await makeRequest(url, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatNewContent(request, role) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return { role, parts: newParts };\n}\nfunction formatGenerateContentInput(params) {\n    if (params.contents) {\n        return params;\n    }\n    else {\n        const content = formatNewContent(params, \"user\");\n        return { contents: [content] };\n    }\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params, \"user\");\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, requestOptions) {\n        this.model = model;\n        this.params = params;\n        this.requestOptions = requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            this._history = params.history.map((content) => {\n                if (!content.role) {\n                    throw new Error(\"Missing role for history item: \" + JSON.stringify(content));\n                }\n                return formatNewContent(content.parts, content.role);\n            });\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}\n     */\n    async sendMessage(request) {\n        var _a, _b;\n        await this._sendPromise;\n        const newContent = formatNewContent(request, \"user\");\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            contents: [...this._history, newContent],\n        };\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, this.requestOptions))\n            .then((result) => {\n            var _a;\n            if (result.response.candidates &&\n                result.response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     */\n    async sendMessageStream(request) {\n        var _a, _b;\n        await this._sendPromise;\n        const newContent = formatNewContent(request, \"user\");\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            contents: [...this._history, newContent],\n        };\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, this.requestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (response.candidates && response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, requestOptions) {\n    const url = new RequestUrl(model, Task.COUNT_TOKENS, apiKey, false);\n    const response = await makeRequest(url, JSON.stringify(Object.assign(Object.assign({}, params), { model })), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const url = new RequestUrl(model, Task.EMBED_CONTENT, apiKey, false);\n    const response = await makeRequest(url, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const url = new RequestUrl(model, Task.BATCH_EMBED_CONTENTS, apiKey, false);\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeRequest(url, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, requestOptions) {\n        this.apiKey = apiKey;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.requestOptions = requestOptions || {};\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     */\n    async generateContent(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings }, formattedParams), this.requestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model\n     * and returns an object containing an iterable stream that iterates\n     * over all chunks in the streaming response as well as\n     * a promise that returns the final aggregated response.\n     */\n    async generateContentStream(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings }, formattedParams), this.requestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        return new ChatSession(this.apiKey, this.model, startChatParams, this.requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     */\n    async countTokens(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return countTokens(this.apiKey, this.model, formattedParams);\n    }\n    /**\n     * Embeds the provided content.\n     */\n    async embedContent(request) {\n        const formattedParams = formatEmbedContentInput(request);\n        return embedContent(this.apiKey, this.model, formattedParams);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     */\n    async batchEmbedContents(batchEmbedContentRequest) {\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, this.requestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n}\n\nexport { BlockReason, ChatSession, FinishReason, GenerativeModel, GoogleGenerativeAI, HarmBlockThreshold, HarmCategory, HarmProbability, TaskType };\n//# sourceMappingURL=index.mjs.map\n"], "mappings": ";;;AAoBA,IAAI;AAAA,CACH,SAAUA,eAAc;AACrB,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,iCAAiC,IAAI;AAClD,EAAAA,cAAa,0BAA0B,IAAI;AAC3C,EAAAA,cAAa,iCAAiC,IAAI;AACtD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAE3B,EAAAA,oBAAmB,kCAAkC,IAAI;AAEzD,EAAAA,oBAAmB,qBAAqB,IAAI;AAE5C,EAAAA,oBAAmB,wBAAwB,IAAI;AAE/C,EAAAA,oBAAmB,iBAAiB,IAAI;AAExC,EAAAA,oBAAmB,YAAY,IAAI;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAKlD,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAExB,EAAAA,iBAAgB,8BAA8B,IAAI;AAElD,EAAAA,iBAAgB,YAAY,IAAI;AAEhC,EAAAA,iBAAgB,KAAK,IAAI;AAEzB,EAAAA,iBAAgB,QAAQ,IAAI;AAE5B,EAAAA,iBAAgB,MAAM,IAAI;AAC9B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAK5C,IAAI;AAAA,CACH,SAAUC,cAAa;AAEpB,EAAAA,aAAY,4BAA4B,IAAI;AAE5C,EAAAA,aAAY,QAAQ,IAAI;AAExB,EAAAA,aAAY,OAAO,IAAI;AAC3B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAKpC,IAAI;AAAA,CACH,SAAUC,eAAc;AAErB,EAAAA,cAAa,2BAA2B,IAAI;AAE5C,EAAAA,cAAa,MAAM,IAAI;AAEvB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,QAAQ,IAAI;AAEzB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,OAAO,IAAI;AAC5B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAAS,uBAAuB,IAAI;AACpC,EAAAA,UAAS,iBAAiB,IAAI;AAC9B,EAAAA,UAAS,oBAAoB,IAAI;AACjC,EAAAA,UAAS,qBAAqB,IAAI;AAClC,EAAAA,UAAS,gBAAgB,IAAI;AAC7B,EAAAA,UAAS,YAAY,IAAI;AAC7B,GAAG,aAAa,WAAW,CAAC,EAAE;AAkB9B,IAAM,0BAAN,cAAsC,MAAM;AAAA,EACxC,YAAY,SAAS;AACjB,UAAM,+BAA+B,OAAO,EAAE;AAAA,EAClD;AACJ;AACA,IAAM,kCAAN,cAA8C,wBAAwB;AAAA,EAClE,YAAY,SAAS,UAAU;AAC3B,UAAM,OAAO;AACb,SAAK,WAAW;AAAA,EACpB;AACJ;AAkBA,IAAM,WAAW;AACjB,IAAM,cAAc;AAKpB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAI;AAAA,CACH,SAAUC,OAAM;AACb,EAAAA,MAAK,kBAAkB,IAAI;AAC3B,EAAAA,MAAK,yBAAyB,IAAI;AAClC,EAAAA,MAAK,cAAc,IAAI;AACvB,EAAAA,MAAK,eAAe,IAAI;AACxB,EAAAA,MAAK,sBAAsB,IAAI;AACnC,GAAG,SAAS,OAAO,CAAC,EAAE;AACtB,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,OAAO,MAAM,QAAQ,QAAQ;AACrC,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,WAAW;AACP,QAAI,MAAM,GAAG,QAAQ,IAAI,WAAW,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI;AAC/D,QAAI,KAAK,QAAQ;AACb,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,mBAAmB;AACxB,SAAO,GAAG,kBAAkB,IAAI,eAAe;AACnD;AACA,eAAe,YAAY,KAAK,MAAM,gBAAgB;AAClD,MAAI;AACJ,MAAI;AACA,eAAW,MAAM,MAAM,IAAI,SAAS,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,cAAc,CAAC,GAAG,EAAE,QAAQ,QAAQ,SAAS;AAAA,MAC9H,gBAAgB;AAAA,MAChB,qBAAqB,iBAAiB;AAAA,MACtC,kBAAkB,IAAI;AAAA,IAC1B,GAAG,KAAK,CAAC,CAAC;AACd,QAAI,CAAC,SAAS,IAAI;AACd,UAAI,UAAU;AACd,UAAI;AACA,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,kBAAU,KAAK,MAAM;AACrB,YAAI,KAAK,MAAM,SAAS;AACpB,qBAAW,IAAI,KAAK,UAAU,KAAK,MAAM,OAAO,CAAC;AAAA,QACrD;AAAA,MACJ,SACO,GAAG;AAAA,MAEV;AACA,YAAM,IAAI,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,UAAU,KAAK,OAAO,EAAE;AAAA,IAC5E;AAAA,EACJ,SACO,GAAG;AACN,UAAM,MAAM,IAAI,wBAAwB,uBAAuB,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7F,QAAI,QAAQ,EAAE;AACd,UAAM;AAAA,EACV;AACA,SAAO;AACX;AAMA,SAAS,kBAAkB,gBAAgB;AACvC,QAAM,eAAe,CAAC;AACtB,OAAK,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,GAAG;AAC/F,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,UAAM,SAAS,gBAAgB;AAC/B,eAAW,MAAM,gBAAgB,MAAM,GAAG,eAAe,OAAO;AAChE,iBAAa,SAAS;AAAA,EAC1B;AACA,SAAO;AACX;AAsBA,SAAS,WAAW,UAAU;AAC1B,WAAS,OAAO,MAAM;AAClB,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,6HAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,uBAAuB,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAClH;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,UAAU;AACvB,MAAI,IAAI,IAAI,IAAI;AAChB,OAAK,MAAM,MAAM,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAC1O,WAAO,SAAS,WAAW,CAAC,EAAE,QAAQ,MAAM,CAAC,EAAE;AAAA,EACnD,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,IAAM,mBAAmB,CAAC,aAAa,YAAY,aAAa,MAAM;AACtE,SAAS,mBAAmB,WAAW;AACnC,SAAQ,CAAC,CAAC,UAAU,gBAChB,iBAAiB,SAAS,UAAU,YAAY;AACxD;AACA,SAAS,wBAAwB,UAAU;AACvC,MAAI,IAAI,IAAI;AACZ,MAAI,UAAU;AACd,OAAK,CAAC,SAAS,cAAc,SAAS,WAAW,WAAW,MACxD,SAAS,gBAAgB;AACzB,eAAW;AACX,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACpF,iBAAW,WAAW,SAAS,eAAe,WAAW;AAAA,IAC7D;AACA,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAC3F,iBAAW,KAAK,SAAS,eAAe,kBAAkB;AAAA,IAC9D;AAAA,EACJ,YACU,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,GAAG;AAC5E,UAAM,iBAAiB,SAAS,WAAW,CAAC;AAC5C,QAAI,mBAAmB,cAAc,GAAG;AACpC,iBAAW,gCAAgC,eAAe,YAAY;AACtE,UAAI,eAAe,eAAe;AAC9B,mBAAW,KAAK,eAAe,aAAa;AAAA,MAChD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAmBA,SAAS,QAAQ,GAAG;AAChB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACvE;AAEA,SAAS,iBAAiB,SAAS,YAAY,WAAW;AACtD,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACpH,WAAS,KAAK,GAAG;AAAE,QAAI,EAAE,CAAC,EAAG,GAAE,CAAC,IAAI,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,UAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AACzI,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,OAAQ,QAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAuBA,IAAM,iBAAiB;AASvB,SAAS,cAAc,UAAU;AAC7B,QAAM,cAAc,SAAS,KAAK,YAAY,IAAI,kBAAkB,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;AAC5F,QAAM,iBAAiB,kBAAkB,WAAW;AACpD,QAAM,CAAC,SAAS,OAAO,IAAI,eAAe,IAAI;AAC9C,SAAO;AAAA,IACH,QAAQ,yBAAyB,OAAO;AAAA,IACxC,UAAU,mBAAmB,OAAO;AAAA,EACxC;AACJ;AACA,eAAe,mBAAmB,QAAQ;AACtC,QAAM,eAAe,CAAC;AACtB,QAAM,SAAS,OAAO,UAAU;AAChC,SAAO,MAAM;AACT,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACN,aAAO,WAAW,mBAAmB,YAAY,CAAC;AAAA,IACtD;AACA,iBAAa,KAAK,KAAK;AAAA,EAC3B;AACJ;AACA,SAAS,yBAAyB,QAAQ;AACtC,SAAO,iBAAiB,MAAM,WAAW,UAAU,6BAA6B;AAC5E,UAAM,SAAS,OAAO,UAAU;AAChC,WAAO,MAAM;AACT,YAAM,EAAE,OAAO,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,CAAC;AACnD,UAAI,MAAM;AACN;AAAA,MACJ;AACA,YAAM,MAAM,QAAQ,WAAW,KAAK,CAAC;AAAA,IACzC;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,kBAAkB,aAAa;AACpC,QAAM,SAAS,YAAY,UAAU;AACrC,QAAM,SAAS,IAAI,eAAe;AAAA,IAC9B,MAAM,YAAY;AACd,UAAI,cAAc;AAClB,aAAO,KAAK;AACZ,eAAS,OAAO;AACZ,eAAO,OAAO,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM;AAC3C,cAAI,MAAM;AACN,gBAAI,YAAY,KAAK,GAAG;AACpB,yBAAW,MAAM,IAAI,wBAAwB,wBAAwB,CAAC;AACtE;AAAA,YACJ;AACA,uBAAW,MAAM;AACjB;AAAA,UACJ;AACA,yBAAe;AACf,cAAI,QAAQ,YAAY,MAAM,cAAc;AAC5C,cAAI;AACJ,iBAAO,OAAO;AACV,gBAAI;AACA,+BAAiB,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,YACxC,SACO,GAAG;AACN,yBAAW,MAAM,IAAI,wBAAwB,iCAAiC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC1F;AAAA,YACJ;AACA,uBAAW,QAAQ,cAAc;AACjC,0BAAc,YAAY,UAAU,MAAM,CAAC,EAAE,MAAM;AACnD,oBAAQ,YAAY,MAAM,cAAc;AAAA,UAC5C;AACA,iBAAO,KAAK;AAAA,QAChB,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAKA,SAAS,mBAAmB,WAAW;AACnC,QAAM,eAAe,UAAU,UAAU,SAAS,CAAC;AACnD,QAAM,qBAAqB;AAAA,IACvB,gBAAgB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AAAA,EAC7F;AACA,aAAW,YAAY,WAAW;AAC9B,QAAI,SAAS,YAAY;AACrB,iBAAW,aAAa,SAAS,YAAY;AACzC,cAAM,IAAI,UAAU;AACpB,YAAI,CAAC,mBAAmB,YAAY;AAChC,6BAAmB,aAAa,CAAC;AAAA,QACrC;AACA,YAAI,CAAC,mBAAmB,WAAW,CAAC,GAAG;AACnC,6BAAmB,WAAW,CAAC,IAAI;AAAA,YAC/B,OAAO,UAAU;AAAA,UACrB;AAAA,QACJ;AAEA,2BAAmB,WAAW,CAAC,EAAE,mBAC7B,UAAU;AACd,2BAAmB,WAAW,CAAC,EAAE,eAAe,UAAU;AAC1D,2BAAmB,WAAW,CAAC,EAAE,gBAC7B,UAAU;AACd,2BAAmB,WAAW,CAAC,EAAE,gBAC7B,UAAU;AAKd,YAAI,UAAU,WAAW,UAAU,QAAQ,OAAO;AAC9C,cAAI,CAAC,mBAAmB,WAAW,CAAC,EAAE,SAAS;AAC3C,+BAAmB,WAAW,CAAC,EAAE,UAAU;AAAA,cACvC,MAAM,UAAU,QAAQ,QAAQ;AAAA,cAChC,OAAO,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,YACxB;AAAA,UACJ;AACA,qBAAW,QAAQ,UAAU,QAAQ,OAAO;AACxC,gBAAI,KAAK,MAAM;AACX,iCAAmB,WAAW,CAAC,EAAE,QAAQ,MAAM,CAAC,EAAE,QAC9C,KAAK;AAAA,YACb;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAkBA,eAAe,sBAAsB,QAAQ,OAAO,QAAQ,gBAAgB;AACxE,QAAM,MAAM,IAAI;AAAA,IAAW;AAAA,IAAO,KAAK;AAAA,IAAyB;AAAA;AAAA,IACnD;AAAA,EAAI;AACjB,QAAM,WAAW,MAAM,YAAY,KAAK,KAAK,UAAU,MAAM,GAAG,cAAc;AAC9E,SAAO,cAAc,QAAQ;AACjC;AACA,eAAe,gBAAgB,QAAQ,OAAO,QAAQ,gBAAgB;AAClE,QAAM,MAAM,IAAI;AAAA,IAAW;AAAA,IAAO,KAAK;AAAA,IAAkB;AAAA;AAAA,IAC5C;AAAA,EAAK;AAClB,QAAM,WAAW,MAAM,YAAY,KAAK,KAAK,UAAU,MAAM,GAAG,cAAc;AAC9E,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,mBAAmB,WAAW,YAAY;AAChD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AAkBA,SAAS,iBAAiB,SAAS,MAAM;AACrC,MAAI,WAAW,CAAC;AAChB,MAAI,OAAO,YAAY,UAAU;AAC7B,eAAW,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,OACK;AACD,eAAW,gBAAgB,SAAS;AAChC,UAAI,OAAO,iBAAiB,UAAU;AAClC,iBAAS,KAAK,EAAE,MAAM,aAAa,CAAC;AAAA,MACxC,OACK;AACD,iBAAS,KAAK,YAAY;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,MAAM,OAAO,SAAS;AACnC;AACA,SAAS,2BAA2B,QAAQ;AACxC,MAAI,OAAO,UAAU;AACjB,WAAO;AAAA,EACX,OACK;AACD,UAAM,UAAU,iBAAiB,QAAQ,MAAM;AAC/C,WAAO,EAAE,UAAU,CAAC,OAAO,EAAE;AAAA,EACjC;AACJ;AACA,SAAS,wBAAwB,QAAQ;AACrC,MAAI,OAAO,WAAW,YAAY,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAM,UAAU,iBAAiB,QAAQ,MAAM;AAC/C,WAAO,EAAE,QAAQ;AAAA,EACrB;AACA,SAAO;AACX;AAqBA,IAAM,eAAe;AAOrB,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,QAAQ,OAAO,QAAQ,gBAAgB;AAC/C,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,WAAW,CAAC;AACjB,SAAK,eAAe,QAAQ,QAAQ;AACpC,SAAK,UAAU;AACf,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,WAAK,WAAW,OAAO,QAAQ,IAAI,CAAC,YAAY;AAC5C,YAAI,CAAC,QAAQ,MAAM;AACf,gBAAM,IAAI,MAAM,oCAAoC,KAAK,UAAU,OAAO,CAAC;AAAA,QAC/E;AACA,eAAO,iBAAiB,QAAQ,OAAO,QAAQ,IAAI;AAAA,MACvD,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa;AACf,UAAM,KAAK;AACX,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,SAAS;AACvB,QAAI,IAAI;AACR,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,SAAS,MAAM;AACnD,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,QAAI;AAEJ,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,gBAAgB,KAAK,SAAS,KAAK,OAAO,wBAAwB,KAAK,cAAc,CAAC,EACjG,KAAK,CAAC,WAAW;AAClB,UAAIC;AACJ,UAAI,OAAO,SAAS,cAChB,OAAO,SAAS,WAAW,SAAS,GAAG;AACvC,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO;AAAA,UAAE,OAAO,CAAC;AAAA;AAAA,UAE5C,MAAM;AAAA,QAAQ,IAAIA,MAAK,OAAO,SAAS,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,EAAE,OAAO;AACzG,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,OAAO,QAAQ;AACjE,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,mCAAmC,iBAAiB,wCAAwC;AAAA,QAC7G;AAAA,MACJ;AACA,oBAAc;AAAA,IAClB,CAAC;AACD,UAAM,KAAK;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kBAAkB,SAAS;AAC7B,QAAI,IAAI;AACR,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,SAAS,MAAM;AACnD,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,UAAM,gBAAgB,sBAAsB,KAAK,SAAS,KAAK,OAAO,wBAAwB,KAAK,cAAc;AAEjH,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,aAAa,EAGxB,MAAM,CAAC,aAAa;AACrB,YAAM,IAAI,MAAM,YAAY;AAAA,IAChC,CAAC,EACI,KAAK,CAAC,iBAAiB,aAAa,QAAQ,EAC5C,KAAK,CAAC,aAAa;AACpB,UAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,SAAS,WAAW,CAAC,EAAE,OAAO;AAExE,YAAI,CAAC,gBAAgB,MAAM;AACvB,0BAAgB,OAAO;AAAA,QAC3B;AACA,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,QAAQ;AAC1D,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,yCAAyC,iBAAiB,wCAAwC;AAAA,QACnH;AAAA,MACJ;AAAA,IACJ,CAAC,EACI,MAAM,CAAC,MAAM;AAId,UAAI,EAAE,YAAY,cAAc;AAG5B,gBAAQ,MAAM,CAAC;AAAA,MACnB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAkBA,eAAe,YAAY,QAAQ,OAAO,QAAQ,gBAAgB;AAC9D,QAAM,MAAM,IAAI,WAAW,OAAO,KAAK,cAAc,QAAQ,KAAK;AAClE,QAAM,WAAW,MAAM,YAAY,KAAK,KAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,cAAc;AAC3H,SAAO,SAAS,KAAK;AACzB;AAkBA,eAAe,aAAa,QAAQ,OAAO,QAAQ,gBAAgB;AAC/D,QAAM,MAAM,IAAI,WAAW,OAAO,KAAK,eAAe,QAAQ,KAAK;AACnE,QAAM,WAAW,MAAM,YAAY,KAAK,KAAK,UAAU,MAAM,GAAG,cAAc;AAC9E,SAAO,SAAS,KAAK;AACzB;AACA,eAAe,mBAAmB,QAAQ,OAAO,QAAQ,gBAAgB;AACrE,QAAM,MAAM,IAAI,WAAW,OAAO,KAAK,sBAAsB,QAAQ,KAAK;AAC1E,QAAM,oBAAoB,OAAO,SAAS,IAAI,CAAC,YAAY;AACvD,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,CAAC;AAAA,EAC9D,CAAC;AACD,QAAM,WAAW,MAAM,YAAY,KAAK,KAAK,UAAU,EAAE,UAAU,kBAAkB,CAAC,GAAG,cAAc;AACvG,SAAO,SAAS,KAAK;AACzB;AAsBA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,QAAQ,aAAa,gBAAgB;AAC7C,SAAK,SAAS;AACd,QAAI,YAAY,MAAM,SAAS,GAAG,GAAG;AAEjC,WAAK,QAAQ,YAAY;AAAA,IAC7B,OACK;AAED,WAAK,QAAQ,UAAU,YAAY,KAAK;AAAA,IAC5C;AACA,SAAK,mBAAmB,YAAY,oBAAoB,CAAC;AACzD,SAAK,iBAAiB,YAAY,kBAAkB,CAAC;AACrD,SAAK,iBAAiB,kBAAkB,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,gBAAgB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,eAAe,GAAG,eAAe,GAAG,KAAK,cAAc;AAAA,EACzL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,sBAAsB,SAAS;AACjC,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,sBAAsB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,eAAe,GAAG,eAAe,GAAG,KAAK,cAAc;AAAA,EAC/L;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,iBAAiB;AACvB,WAAO,IAAI,YAAY,KAAK,QAAQ,KAAK,OAAO,iBAAiB,KAAK,cAAc;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,YAAY,SAAS;AACvB,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,YAAY,KAAK,QAAQ,KAAK,OAAO,eAAe;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,aAAa,SAAS;AACxB,UAAM,kBAAkB,wBAAwB,OAAO;AACvD,WAAO,aAAa,KAAK,QAAQ,KAAK,OAAO,eAAe;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,mBAAmB,0BAA0B;AAC/C,WAAO,mBAAmB,KAAK,QAAQ,KAAK,OAAO,0BAA0B,KAAK,cAAc;AAAA,EACpG;AACJ;AAsBA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,aAAa,gBAAgB;AAC5C,QAAI,CAAC,YAAY,OAAO;AACpB,YAAM,IAAI,wBAAwB,0FACiC;AAAA,IACvE;AACA,WAAO,IAAI,gBAAgB,KAAK,QAAQ,aAAa,cAAc;AAAA,EACvE;AACJ;", "names": ["HarmCategory", "HarmBlockThreshold", "HarmProbability", "BlockReason", "FinishReason", "TaskType", "Task", "_a"]}