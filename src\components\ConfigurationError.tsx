import { Alert<PERSON><PERSON>gle, ExternalLink, RefreshCw, ArrowLeft } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface ConfigurationErrorProps {
  error: string;
}

export function ConfigurationError({ error }: ConfigurationErrorProps) {
  const { prevStep } = useApp();

  const isAPIKeyError = error.includes('API key');

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Ícone de erro */}
        <div className="w-24 h-24 mx-auto mb-8 bg-red-100 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-12 h-12 text-red-600" />
        </div>

        {/* Título */}
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
          ⚠️ Configuração Necessária
        </h1>

        {/* Mensagem de erro */}
        <div className="bg-red-100 border border-red-300 rounded-lg p-6 mb-8">
          <p className="text-red-800 font-medium mb-2">
            Erro encontrado:
          </p>
          <p className="text-red-700 text-sm">
            {error}
          </p>
        </div>

        {isAPIKeyError ? (
          <>
            {/* Instruções para API Key */}
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8 text-left">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                🔑 Como Configurar a API do Google Gemini
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Obtenha uma API Key gratuita</h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Acesse o Google AI Studio e crie sua chave de API gratuita:
                    </p>
                    <a 
                      href="https://makersuite.google.com/app/apikey" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Abrir Google AI Studio
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Configure no projeto</h3>
                    <p className="text-gray-600 text-sm mb-2">
                      Crie um arquivo <code className="bg-gray-100 px-2 py-1 rounded">.env</code> na raiz do projeto:
                    </p>
                    <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm">
                      VITE_GOOGLE_API_KEY=sua_chave_aqui
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Reinicie o servidor</h3>
                    <p className="text-gray-600 text-sm">
                      Pare o servidor (Ctrl+C) e execute novamente:
                    </p>
                    <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm mt-2">
                      npm run dev
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Informações sobre limites gratuitos */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-green-800 mb-2">
                ✅ Limites Gratuitos Generosos
              </h3>
              <div className="text-green-700 text-sm space-y-1">
                <p>• 60 requisições por minuto</p>
                <p>• 1.500 requisições por dia</p>
                <p>• Totalmente gratuito para uso pessoal</p>
              </div>
            </div>
          </>
        ) : (
          /* Outros tipos de erro */
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              🔧 Possíveis Soluções
            </h2>
            <div className="text-left space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                <p className="text-gray-700">Verifique sua conexão com a internet</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                <p className="text-gray-700">Confirme se a API key está correta</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                <p className="text-gray-700">Verifique se não atingiu o limite de requisições</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                <p className="text-gray-700">Tente novamente em alguns minutos</p>
              </div>
            </div>
          </div>
        )}

        {/* Ações */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={prevStep}
            className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Voltar ao Teste
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            Recarregar Página
          </button>
        </div>

        {/* Nota importante */}
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">
            <strong>Importante:</strong> Esta aplicação utiliza apenas análise real com IA. 
            Não fornecemos resultados falsos ou genéricos. Configure a API para obter insights genuínos.
          </p>
        </div>
      </div>
    </div>
  );
}
